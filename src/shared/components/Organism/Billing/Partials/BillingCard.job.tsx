import React, { useMemo } from 'react';
import dayjs from 'dayjs';
import Flex from '@shared/uikit/Flex';
import Text from '@shared/components/molecules/Text/Text';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { type BillingCardProps } from '../types';
import classes from './partials.module.scss';
import PlanInfoCard from '@shared/components/molecules/PlanCad/PlanCard.infoCard';
import PlanCardPrice from '@shared/components/molecules/PlanCad/PlanCard.price';

export default function JobBillingCard({ data, children }: BillingCardProps) {
  const { t } = useTranslation();

  const cardNumber = useMemo(() => {
    if (!data?.last4digits) return undefined;
    return `**** **** **** ${data?.last4digits}`;
  }, [data?.last4digits]);

  return (
    <Flex className={classes.mainBillingCardcardWrapper}>
      <PlanInfoCard
        Icon={data?.Logo}
        label={data?.label}
        cardProps={{
          subTitle: data?.timeSpan,
          valueProps: { color: 'secondaryDisabledText' },
          className: classes.planInfoCard,
        }}
      >
        <PlanCardPrice
          price={data?.price}
          priceProps={{ size: 16, height: 19, font: '600' }}
          priceUnit={data?.priceUnit}
          priceUnitProps={{
            size: 14,
            height: 16,
            isWordWrap: false,
            isTruncated: false,
          }}
          wrapperClassName={classes.planCardPriceWrapper}
        />
      </PlanInfoCard>
      <Flex className={classes.bodyWrapper}>
        <Text label={t('invoice_ID')} value={data?.invoiceNumber} hideIfEmpty />
        <Text label={t('total_cap')} value={data?.price} hideIfEmpty />
        <Text
          label={t('payment_date')}
          value={dayjs(data?.paymentDate).format('MMM DD, YYYY')}
          hideIfEmpty
        />
        <Text label={t('card_number')} value={cardNumber} hideIfEmpty />
        <Text
          label={t('name_on_payment_method')}
          value={data?.nameOnCard || '-'}
          hideIfEmpty
        />
      </Flex>
      {children}
    </Flex>
  );
}
