import React from 'react';
import Typography from '@shared/uikit/Typography';
import Avatar from '@shared/uikit/Avatar';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import type { ICandidateSummaryOption } from '@shared/types/candidates';
import Flex from '@shared/uikit/Flex';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import useTranslation from '@shared/utils/hooks/useTranslation';
import dateFromNow from '@shared/utils/toolkit/dateFromNow';
import classes from './JobControl.module.scss';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';

export const SummaryItem = ({ item }: { item: ICandidateSummaryOption }) => {
  const { t } = useTranslation();

  return (
    <InfoCard
      wrapperClassName={classes.infoCardWrapper}
      textWrapperClassName={classes.infoCardTextWrapper}
      valueProps={{ color: 'primaryText' }}
      title={item?.jobTitle}
      avatar={
        <Avatar
          size="smd"
          imgSrc={item?.pageCroppedImageUrl}
          isCompany={item?.type !== 'ORIGINAL_CANDIDATE'}
          bordered
          className={classes.InfoCardAvatar}
          avatarInnerClassName={classes.innerCardAvatar}
        />
      }
      titleProps={{
        color: 'white',
        fontWeight: 700,
        size: 16,
      }}
      subTitle={
        <Flex flexDir="row">
          {item?.type !== 'ORIGINAL_CANDIDATE' ? (
            <>
              <Typography color="disabledGray" fontWeight={700}>
                {item?.pageTitle}
              </Typography>
              <DividerVertical className={classes.divider} />
              {item?.type === 'APPLICANT' ? (
                <Typography color="disabledGrayDark">
                  {translateReplacer(t('applied_time_ago'), [
                    item?.dateTime
                      ? dateFromNow(String(item.dateTime), false, t)
                      : '',
                  ])}
                </Typography>
              ) : (
                <Typography color="pendingOrange">{t('candidate')}</Typography>
              )}
            </>
          ) : (
            <Typography>{t('candidate_mode')}</Typography>
          )}
        </Flex>
      }
    />
  );
};
