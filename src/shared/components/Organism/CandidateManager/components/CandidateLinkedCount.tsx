import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import Icon from '@shared/uikit/Icon';
import Skeleton from '@shared/uikit/Skeleton';
import Typography from '@shared/uikit/Typography';
import { countUserParticipation } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import { useSearchParams } from 'next/navigation';
import React from 'react';

function CandidateLinkedCount() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const currentEntityId = searchParams.get('currentEntityId');
  const appDispatch = useGlobalDispatch();

  const { data, isLoading } = useReactQuery({
    action: {
      apiFunc: () => countUserParticipation({ id: currentEntityId ?? '' }),
      key: [QueryKeys.getPopularPeopleCategories],
    },
    config: {
      enabled: !!currentEntityId,
    },
  });

  if (isLoading) {
    return <Skeleton className="w-full h-40 rounded-lg" />;
  }

  if (!data?.value || data?.value === '0') return null;

  return (
    <Flex
      flexDir="row"
      className="justify-between !py-[9px] px-12 rounded-lg bg-success_10"
    >
      <Flex flexDir="row" className="gap-8" alignItems="center">
        <Icon
          name="user-applicants"
          color="messageSuccess"
          size={20}
          type="far"
        />
        <Typography size={14} font="500" color="smoke_coal">
          {translateReplacer(
            t('candidate_linked_to_your_jobs'),
            data?.value || ''
          )}
        </Typography>
      </Flex>

      <Flex flexDir="row" className="gap-12" alignItems="center">
        <Typography
          size={15}
          font="700"
          color="brand"
          className="cursor-pointer"
          onClick={() => appDispatch({ type: 'TOGGLE_SETTINGS_MODAL' })}
        >
          {t('view_jobs')}
        </Typography>
      </Flex>
    </Flex>
  );
}

export default CandidateLinkedCount;
