import {
  useGlobalDispatch,
  useGlobalState,
} from '@shared/contexts/Global/global.provider';
import FixedRightSideModal from '@shared/uikit/Modal/FixedRightSideModalDialog';
import ModalBody from '@shared/uikit/Modal/ModalBody';
import ModalHeaderSimple from '@shared/uikit/Modal/ModalHeaderSimple';
import { getCandidacyLinkedJobs } from '@shared/utils/api/jobs';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useRouter, useSearchParams } from 'next/navigation';
import React from 'react';
import Tooltip from '@shared/uikit/Tooltip';
import IconButton from '@shared/uikit/Button/IconButton';
import Typography from '@shared/uikit/Typography';
import Flex from '@shared/uikit/Flex';
import { ProjectProps } from '@shared/types/project';
import { JobProps } from '@shared/types/jobsProps';
import Icon from '@shared/uikit/Icon';
import ItemComponent from '../AsyncPickerModal/components/ItemComponent';

function LinkedJobModal() {
  const { t } = useTranslation();
  const searchParams = useSearchParams();
  const currentEntityId = searchParams.get('currentEntityId');
  const appDispatch = useGlobalDispatch();
  const isLinkedJobOpen = useGlobalState('isSettingModalOpen');
  const router = useRouter();

  const renderSubtitle = (item: JobProps) =>
    item.projects?.map((proj: ProjectProps) => proj.title).join(', ');

  const linkedJobsQuery = useReactQuery({
    action: {
      apiFunc: () =>
        getCandidacyLinkedJobs(currentEntityId || '').then((items) =>
          items.map((c) => ({ ...c.job, id: String(c.job.id) }))
        ),
      key: [QueryKeys.getCandidateRecruiterJobsList, currentEntityId],
    },
    config: {
      enabled: !!currentEntityId,
    },
  });

  const onClose = () => {
    if (isLinkedJobOpen) appDispatch({ type: 'TOGGLE_SETTINGS_MODAL' });
  };

  const handleRedirect = (id: string) => {
    onClose();
    appDispatch({ type: 'TOGGLE_CANDIDATE_MANAGER' });
    router.push(`/pipelines/${id}?currentEntityId=${currentEntityId}`);
  };

  return (
    <FixedRightSideModal
      wide
      onClickOutside={() => onClose()}
      onClose={onClose}
      isOpenAnimation
      fullBackdrop
    >
      <ModalHeaderSimple
        hideBack
        // title={t('linked_job')}
        title={
          <Flex flexDir="row" className="gap-8" alignItems="center">
            <Typography font="700" size={24} color="smoke_coal">
              {t('linked_jobs')}
            </Typography>
            <Tooltip
              // triggerWrapperClassName={classes.tooltip}
              placement="top"
              trigger={
                <IconButton
                  colorSchema="transparent1"
                  type="far"
                  name="info-circle"
                  size="md15"
                />
              }
            >
              <Typography
                size={13}
                font="400"
                height={17}
                color="tooltipText"
                className="w-[179px] text-center"
                //   className={classes.tooltipText}
              >
                {t('linked_jobs_tooltip')}
              </Typography>
            </Tooltip>
          </Flex>
        }
      />

      <ModalBody>
        {linkedJobsQuery?.data?.map((item) => (
          <ItemComponent
            image={item.pageCroppedImageUrl}
            key={item?.id}
            title={item.title}
            subTitle={renderSubtitle(item)}
            onClick={() => handleRedirect(item.id)}
          >
            <Icon name="chevron-right" type="fas" size={16} />
          </ItemComponent>
        ))}
      </ModalBody>
    </FixedRightSideModal>
  );
}

export default LinkedJobModal;
