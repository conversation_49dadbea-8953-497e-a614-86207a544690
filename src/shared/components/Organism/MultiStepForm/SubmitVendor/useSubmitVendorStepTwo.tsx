import { QueryKeys } from 'shared/utils/constants/enums';
import useTranslation from 'shared/utils/hooks/useTranslation';
import Flex from 'shared/uikit/Flex';
import { useCallback, useState } from 'react';

import RichText from '@shared/uikit/RichText';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { postMultiJobSubmit } from '@shared/utils/api/company';
import { toast } from 'react-toastify';
import { JobPiplineData } from '@shared/types/pipelineProps';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import Skeleton from '@shared/uikit/Skeleton';
import { jobsEndpoints } from '@shared/utils/constants';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';
import classes from './useSendStep.module.scss';
import ItemComponent from '../../AsyncPickerModal/components/ItemComponent';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type Args = {
  checkedIds: string[];
};

export function useSubmitVendorStepTwo({ checkedIds }: Args): SingleDataItem[] {
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { data: submitVendorData } = useMultiStepFormState('submitToVendor');
  const jobId = submitVendorData?.jobId;
  const [description, setDescription] = useState('');

  const { data: job, isLoading: isLoadingJob } = useReactQuery<JobPiplineData>({
    action: {
      key: [QueryKeys.getPipeline, jobId],
      url: jobsEndpoints.getPipeline(jobId.toString()),
    },
  });

  const handleRichTextChanged = useCallback((result: string) => {
    setDescription(result);
  }, []);

  const onClose = () => closeMultiStepForm('submitToVendor');

  const onAlert = (
    title: string,
    message: string,
    type: 'success' | 'error'
  ) => {
    toast({
      type,
      icon: `${type === 'success' ? 'check' : 'times'}-circle`,
      title,
      message,
    });
  };

  const onError = (error: any) => {
    let message = t('error_moving_candidates');
    if (error?.response?.data?.message) {
      message = error.response.data.message;
    }
    onAlert(t('error'), message, 'error');
  };

  const { mutate: mutatePostMultiJobSubmit, isLoading } = useMutation({
    mutationFn: postMultiJobSubmit,
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getVendorsIncluded],
      });
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getVendorsExcluded, jobId],
      });
      onAlert(
        t('multiJobSubmittedTitle'),
        t('multiJobSubmittedSubTitle'),
        'success'
      );
      onClose();
    },
    onError,
  });

  const onSubmit = () => {
    mutatePostMultiJobSubmit({
      description,
      jobId,
      vendorIds: checkedIds,
    });
  };

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <TwoButtonFooter
      submitLabel={t('submit')}
      secondaryButtonLabel={t('discard')}
      disabledSubmit={isLoading}
      onSubmitClick={onSubmit}
      secondaryButtonOnClick={() => setStep((prev) => prev - 1)}
    />
  );

  const getHeaderProps: SingleDataItem['getHeaderProps'] = ({ setStep }) => ({
    title: t('add_comment'),
    hideBack: false,
    noCloseButton: true,
    belowContent: (
      <Flex className="p-12 pb-0">
        {isLoadingJob ? (
          <Skeleton className="h-[56px] w-full rounded-lg" />
        ) : (
          <ItemComponent
            key={job?.job?.id}
            image={job?.job?.pageCroppedImageUrl}
            title={job?.job?.title ?? ''}
            subTitle={job?.job?.pageUsername ?? ''}
            subTitleIcon="projects-light"
          />
        )}
      </Flex>
    ),
    backButtonProps: {
      onClick: () => setStep((prev) => prev - 1),
    },
  });

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,
      renderBody: ({ setStep, values }) => (
        <Flex flexDir="column" className="gap-20 flex-1">
          <RichText
            spaceFromEmoji
            label={t('any_comment_about_job')}
            labelProps={{ size: 15 }}
            variant="comment-input"
            value={description}
            changeWithDebounce
            onChange={handleRichTextChanged}
            fixMentionDropdown={false}
            className={classes.richText}
            isFocus
            showEmoji={false}
          />
        </Flex>
      ),
      renderFooter,
    },
  ];

  return data;
}
