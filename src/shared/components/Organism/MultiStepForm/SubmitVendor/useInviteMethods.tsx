import { useState, type Dispatch, type SetStateAction } from 'react';
import useTranslation from 'shared/utils/hooks/useTranslation';
import event from 'shared/utils/toolkit/event';
import Flex from 'shared/uikit/Flex';
import Typography from 'shared/uikit/Typography';
import eventKeys from 'shared/constants/event-keys';
import useBackToModal from '@shared/hooks/useBackToModal';
import {
  closeMultiStepForm,
  useMultiStepFormState,
} from '@shared/hooks/useMultiStepForm';
import type { SuggestSubmitToVendor } from '@shared/types/submitVendor';
import {
  getSuggestCompany,
  getVendorsExcluded,
  getVendorsIncluded,
} from '@shared/utils/api/company';
import { QueryKeys } from '@shared/utils/constants';
import useReactQuery from '@shared/utils/hooks/useReactQuery';
import CheckBox from '@shared/uikit/CheckBox';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import MenuItem from '@shared/uikit/MenuItem';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import ItemComponent from '../../AsyncPickerModal/components/ItemComponent';
import { TwoButtonFooter } from '../ProfileSections/Components/TwoButtonFooter';
import type { MultiStepFormProps } from '../MultiStepForm';
import SubmitVendorItemSkeleton from './SubmitVendorItemSkeleton';

type SingleDataItem = {
  stepKey: string;
  getHeaderProps?: Exclude<MultiStepFormProps['getHeaderProps'], undefined>;
  renderBody: Exclude<MultiStepFormProps['renderBody'], undefined>;
  renderFooter: Exclude<MultiStepFormProps['renderFooter'], undefined>;
};

type Args = {
  checkedIds: string[];
  setCheckedIds: Dispatch<SetStateAction<string[]>>;
};
const MAXIMUM_SUBMISSION = 50;

export function useSubmitVendorStepOne({
  checkedIds,
  setCheckedIds,
}: Args): SingleDataItem[] {
  const { t } = useTranslation();
  const { hasBackModal, backToModal } = useBackToModal('createEntityPanel');
  const [searchText, setSearchText] = useState<string>('');

  const { data: submitVendorData } = useMultiStepFormState('submitToVendor');

  const { content: includedJobs, isLoading: isLoadingIncluded } =
    usePaginateQuery<any>({
      action: {
        apiFunc: () => getVendorsIncluded(),
        key: [QueryKeys.getVendorsIncluded],
        params: {
          page: 0,
          size: 10,
        },
      },
    });

  const { data: excludedJobs, isLoading: isLoadingExcluded } =
    useReactQuery<any>({
      action: {
        apiFunc: () => getVendorsExcluded(submitVendorData?.jobId),
        key: [QueryKeys.getVendorsExcluded, String(submitVendorData?.jobId)],
      },
      config: {
        onSuccess: (data) => {
          setCheckedIds(data);
        },
      },
    });

  const excludedIds = excludedJobs || [];
  const includedIds = includedJobs?.map((item) => item?.pageInfo?.id);

  const { data: suggestCompany, isLoading: isLoadingSuggestCompany } =
    useReactQuery<SuggestSubmitToVendor[]>({
      action: {
        apiFunc: getSuggestCompany as any,
        key: [QueryKeys.getSuggestCompany, includedIds, searchText],
        params: {
          includedId: includedIds,
          text: searchText,
        },
      },
      config: {
        enabled: !!includedIds?.length,
      },
    });

  const isLoading =
    isLoadingSuggestCompany || isLoadingIncluded || isLoadingExcluded;

  const getHeaderProps: SingleDataItem['getHeaderProps'] = () => ({
    title: t('submit_to_vendor'),
    hideBack: !hasBackModal,
    noCloseButton: hasBackModal,
    belowContent: (
      <Flex flexDir="column" className="gap-20 p-20 !pb-0">
        <SearchInputV2
          placeholder={t('search_submit_vendor')}
          onChange={(value) => setSearchText(value)}
          className="w-full"
          value={searchText}
        />
        <MenuItem
          className="!bg-gray_5"
          title={t('maximum_multi_submissions')}
          iconName="info-circle"
          // titleClassName={classes.info_title}
        />
      </Flex>
    ),
    backButtonProps: {
      onClick: () => {
        backToModal();
        closeMultiStepForm('submitToVendor');
        event.trigger(eventKeys.closeModal);
      },
    },
  });

  const renderFooter: SingleDataItem['renderFooter'] = ({ setStep }) => (
    <Flex flexDir="column" className="gap-12">
      <Typography
        color="secondaryDisabledText"
        size={12}
        fontWeight={400}
        className="text-end"
      >
        {checkedIds?.length}/{MAXIMUM_SUBMISSION}
      </Typography>
      <TwoButtonFooter
        submitLabel={t('next')}
        secondaryButtonLabel={t('discard')}
        disabledSubmit={!checkedIds?.length}
        onSubmitClick={() => setStep((prev) => prev + 1)}
        secondaryButtonOnClick={() => closeMultiStepForm('submitToVendor')}
      />
    </Flex>
  );

  const handleCheckBox = (id: string, isSelected: boolean) => {
    if (isSelected) {
      if (checkedIds?.length >= MAXIMUM_SUBMISSION) return;
      setCheckedIds((prev) => [...prev, id]);
    } else {
      setCheckedIds((prev) => prev?.filter((val) => val !== id));
    }
  };

  const data: Array<SingleDataItem> = [
    {
      stepKey: '2',
      getHeaderProps,

      renderBody: () =>
        isLoading ? (
          <SubmitVendorItemSkeleton />
        ) : (
          <Flex flexDir="column">
            {suggestCompany?.map((item) => (
              <ItemComponent
                key={item?.id}
                image={item?.croppedImageUrl}
                title={item.title}
                subTitle={item?.username}
              >
                <CheckBox
                  value={checkedIds.find((val) => item?.id === val)}
                  disabled={excludedJobs?.find((val) => item?.id === val)}
                  onChange={(isSelected: boolean) =>
                    handleCheckBox(item?.id, isSelected)
                  }
                />
              </ItemComponent>
            ))}
          </Flex>
        ),
      renderFooter,
    },
  ];

  return data;
}
