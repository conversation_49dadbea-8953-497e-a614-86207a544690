import * as React from 'react';
import Button from 'shared/uikit/Button';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Flex from '@shared/uikit/Flex';
import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';
import type { SingleJobAPIProps } from '@shared/types/jobsProps';
import Tooltip from '@shared/uikit/Tooltip';
import IconButton from '@shared/uikit/Button/IconButton';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';

interface BusinessJobCardActionsProps {
  job: SingleJobAPIProps;
}

const BusinessJobCardActions: React.FunctionComponent<
  BusinessJobCardActionsProps
> = ({ job }) => {
  const { t } = useTranslation();
  const { onAction } = useRecruiterJobMoreActions({
    exclude: ['submit_to_vendor'],
  });
  const globalDispatch = useGlobalDispatch();

  return (
    <Flex className="!flex-row gap-12">
      <Button
        label={t('share')}
        leftIcon="share"
        leftType="far"
        schema="semi-transparent"
        fullWidth
        onClick={() => onAction('share', job)}
      />
      {job.status === 'UNPUBLISHED' ? (
        <Button
          label={t('edit')}
          onClick={() => onAction('edit', job)}
          rightIcon="edit"
          fullWidth
        />
      ) : (
        <Button
          disabled
          label={t('track')}
          rightIcon="chevron-right"
          fullWidth
        />
      )}
      <Tooltip
        placement="top"
        trigger={
          <IconButton
            name="assignees"
            type="far"
            variant="rectangle"
            colorSchema="graySecondary"
            onClick={() =>
              globalDispatch({
                type: 'OPEN_EDIT_ASSIGNEE_MODAL',
                payload: { entity: job, type: 'job' },
              })
            }
          />
        }
      >
        {t('add_assignee')}
      </Tooltip>
    </Flex>
  );
};

export default BusinessJobCardActions;
