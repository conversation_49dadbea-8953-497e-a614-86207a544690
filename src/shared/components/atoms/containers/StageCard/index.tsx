import Flex from '@shared/uikit/Flex';
import type { TypographyProps } from '@shared/uikit/Typography';
import cnj from '@shared/uikit/utils/cnj';
import type { colorsKeys } from 'shared/uikit/helpers/theme';
import dayjs from 'dayjs';
import relativeTime from 'dayjs/plugin/relativeTime';
import React from 'react';
import { Card } from './partials/StageCard.Card';
import { Header } from './partials/StageCard.Header';
import { Content } from './partials/StageCard.Content';
import { CardSkeleton } from './partials/StageCard.CardSkeleton';

dayjs.extend(relativeTime);

export interface StageCardProps {
  children?: ReactNode;
  className?: string;
  width?: number;
  dataStageId?: string;
}

export namespace StageCardProps {
  export interface Header {
    title: string;
    badge?: {
      color: colorsKeys;
      count: number;
    };
    children?: ReactNode;
    className?: string;
    leftAction?: React.ReactNode;
  }
  export interface Content {
    children?: ReactNode;
    className?: string;
    ref?: React.Ref<HTMLImageElement>;
  }
  export interface Card {
    image?: string;
    fullName: string;
    subtitle: string;
    id: string;
    rate: number;
    automated?: boolean;
    date: string;
    notesCount?: number;
    todosCount?: number;
    meetingsCount?: number;
    className?: string;
    labelProps?: TypographyProps;
    classNames?: {
      root?: string;
      avatarWrapper?: string;
      ratingWrapper?: string;
      badgesWrapper?: string;
    };
    action?: React.ReactNode;
    onClick?: (e: any) => void;
  }
  export interface CardSkeleton {
    className?: string;
    classNames?: {
      root?: string;
      avatarWrapper?: string;
      ratingWrapper?: string;
      badgesWrapper?: string;
    };
  }
}

const StageCard = ((props) => {
  const { children, className, dataStageId, ref, width = 375 } = props;
  return (
    <Flex
      className={cnj('relative', className)}
      style={{ width, minWidth: width }}
      ref={ref as any}
      data-stage-id={dataStageId}
    >
      {children}
    </Flex>
  );
}) as React.ForwardRefExoticComponent<
  StageCardProps & React.RefAttributes<HTMLDivElement>
> & {
  Header: React.FC<StageCardProps.Header>;
  Content: React.FC<StageCardProps.Content> &
    React.ForwardRefExoticComponent<
      StageCardProps.Content & React.RefAttributes<HTMLDivElement>
    >;
  Card: React.FC<StageCardProps.Card>;
  CardSkeleton: React.FC<StageCardProps.CardSkeleton>;
};
StageCard.displayName = 'StageCard';

StageCard.Card = Card;

StageCard.Header = Header;

StageCard.Content = Content as React.FC<StageCardProps.Content> &
  React.ForwardRefExoticComponent<
    StageCardProps.Content & React.RefAttributes<HTMLDivElement>
  >;

StageCard.Header.displayName = 'StageCard.Header';
StageCard.Content.displayName = 'StageCard.Content';
StageCard.Card.displayName = 'StageCard.Card';
StageCard.CardSkeleton = CardSkeleton;
StageCard.CardSkeleton.displayName = 'StageCard.CardSkeleton';

export default StageCard;
