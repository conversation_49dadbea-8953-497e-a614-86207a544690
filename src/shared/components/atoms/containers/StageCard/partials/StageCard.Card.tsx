import CardBadge from '@shared/components/molecules/CardBadge/CardBadge';
import ObjectInfoCardItem from '@shared/components/molecules/ObjectInfoCard/ObjectInfoCard.Item';
import Rating from '@shared/components/molecules/Rating';
import Avatar from '@shared/uikit/Avatar';
import DateView from '@shared/uikit/DateView';
import Divider from '@shared/uikit/Divider';
import Flex from '@shared/uikit/Flex';
import Typography from '@shared/uikit/Typography';
import OverflowTip from '@shared/uikit/Typography/OverflowTip';
import cnj from '@shared/uikit/utils/cnj';
import { AutomationIcon } from '@shared/components/atoms/AutomationIcon';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';
import Icon from '@shared/uikit/Icon';
import type { StageCardProps } from '..';

export const Card = (props: StageCardProps.Card) => {
  const {
    date,
    fullName,
    subtitle,
    image,
    meetingsCount,
    notesCount,
    rate,
    id,
    todosCount,
    className,
    labelProps,
    classNames,
    action,
    automated,
    onClick,
  } = props;

  const searchParams = useSearchParams();
  const currentEntityId = searchParams.get('currentEntityId');
  const isSelected = currentEntityId === id;

  return (
    <Flex
      id={`card-${id}`} // Important: Add unique ID for each card
      className={cnj(
        'bg-busbrand_10 p-12 gap-12 border border-solid border-techGray_20 rounded-[12px] transition-all duration-200',
        isSelected && 'bg-error_5',
        className,
        classNames?.root
      )}
      onClick={onClick}
    >
      <Flex
        className={cnj('!flex-row gap-10 relative', classNames?.avatarWrapper)}
      >
        <Avatar imgSrc={image} size="smd" />
        {automated && <AutomationIcon />}
        <Flex className="gap-4 overflow-hidden">
          <OverflowTip size={16} height={20} font="700">
            {fullName}
          </OverflowTip>
          <ObjectInfoCardItem title={subtitle} icon="job" iconSize={14} />
        </Flex>
        {action}
      </Flex>
      <Divider />
      <Flex
        className={cnj(
          '!flex-row gap-12 items-center',
          classNames?.ratingWrapper
        )}
      >
        <Rating
          precision={0.1}
          className="py-3"
          value={rate}
          spacing={6}
          size={18}
          readOnly
        />
        {labelProps && (
          <Typography
            {...labelProps}
            className={cnj('ml-auto', labelProps.className)}
            size={labelProps.size ?? 14}
            height={labelProps.height ?? 16}
            font={labelProps.font ?? '500'}
          />
        )}
      </Flex>
      <Flex
        className={cnj(
          '!flex-row gap-8 items-center',
          classNames?.badgesWrapper
        )}
      >
        <CardBadge
          value={String(notesCount)}
          iconsDetails={{ iconName: 'note', iconSize: 16 }}
        />
        <CardBadge
          value={String(10)}
          iconsDetails={{ iconName: 'checklist', iconSize: 16 }}
        />
        <CardBadge
          value={String(meetingsCount)}
          iconsDetails={{ iconName: 'meeting', iconSize: 16 }}
        />
        <DateView
          value={date}
          size={12}
          color="secondaryDisabledText"
          className="ml-auto"
        />
      </Flex>
    </Flex>
  );
};
