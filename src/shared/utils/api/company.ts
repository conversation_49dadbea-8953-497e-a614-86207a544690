import { SuggestSubmitToVendor } from '@shared/types/submitVendor';
import type { PaginateResponse } from '@shared/types/response';
import request from '../toolkit/request';
import { Endpoints } from '../constants';

export const getVendorsIncluded = async <T>(params?: {
  page?: number;
  size?: number;
}): Promise<PaginateResponse<T>> => {
  const { data } = await request.get<PaginateResponse<T>>(
    Endpoints.App.companyService.getVendorsIncluded,
    {
      params,
    }
  );
  return data;
};

export const getVendorsExcluded = async (id: string) => {
  const { data } = await request.get(
    Endpoints.App.companyService.getVendorsExcluded(id)
  );
  return data;
};

export const getSuggestCompany = async ({
  params,
}: {
  params?: {
    text: string;
    excludedId: number[];
    includedId: number[];
  };
} = {}): Promise<SuggestSubmitToVendor[]> => {
  const { data } = await request.get(Endpoints.App.search.getSuggestCompany, {
    params,
  });

  return data;
};

export const postMultiJobSubmit = async (params: {
  vendorIds: string[];
  jobId: number;
  description: string;
}): Promise<any> => {
  const { data } = await request.post(
    Endpoints.App.companyService.postMultiJobSubmit,
    {
      ...params,
    }
  );
  return data;
};
