import { ProviderType } from 'shared/components/molecules/EventsIntegration/utils/type';

const QueryKeys = {
  authUser: 'authUser',
  aboutSections: 'aboutSections',
  allUsers: 'allUsers',
  objectDetail: 'objectDetail',
  myPagesNotifications: 'myPagesNotifications',
  myPagesMessageNotifications: 'myPagesMessageNotifications',
  businessPage: 'businessPage',
  homeFeedList: 'homeFeedList',
  discoverPlace: 'discoverPlace',
  userExperiences: 'experiences',
  collections: 'collections',
  collection: 'collection',
  collectionFeed: 'collectionFeed',
  postComments: 'postComments',
  commentReplies: 'commentReplies',
  userHashtags: 'userHashtags',
  popularHashtags: 'popularHashtags',
  getViewCount: 'getViewCount',
  getPostInteractionsCount: 'getPostInteractionsCount',
  getFollowDiagram: 'getFollowDiagram',
  getViewDiagram: 'getViewDiagram',
  getTopPosts: 'getTopPosts',
  getOpenForJobOpportunities: 'getOpenForJobOpportunities',
  getSuggestionPages: 'getSuggestionPages',
  getMyFollowingPages: 'getMyFollowingPages',
  getSuggestionPeople: 'getSuggestionPeople',
  getMyFollowRequests: 'getMyFollowRequests',
  getMyFollowPeopleRequest: 'getMyFollowPeopleRequest',
  getMyFollowPageRequest: 'getMyFollowPageRequest',
  getMyFollowingPeople: 'getMyFollowingPeople',
  getMyFollowerPeople: 'getMyFollowerPeople',
  getMyFollowerPages: 'getMyFollowerPages',
  getPendingRequests: 'getPendingRequests',
  getPopularPageCategories: 'getPopularPageCategories',
  getFilteredPages: 'getFilteredPages',
  getCategory: 'getCategory',
  getFeedDetail: 'getFeedDetail',
  readyToPostFeed: 'readyToPostFeed',
  followers: 'followers',
  followings: 'followings',
  followRequests: 'followRequests',
  postActionsStatus: 'postActionsStatus',
  pageMembers: 'pageMembers',
  userAllPosts: 'userAllPosts',
  userPosts: 'userPosts',
  userHighlights: 'userHighlights',
  feedSidebarHighlights: 'feedSidebarHighlights',
  postReactions: 'postOrCommentReactions',
  suggestObjects: 'suggestObjects',
  suggestHashtags: 'suggestHashtags',
  searchUsers: 'searchUsers',
  searchPages: 'searchPages',
  searchHashtags: 'searchHashtags',
  searchPosts: 'searchPosts',
  searchCandidates: 'searchCandidates',
  userNetworkRelation: 'UserNetworkRelation',
  upcomingBirthDays: 'upcomingBirthDays',
  getVendorsExcluded: 'getVendorsExcluded',
  getVendorsIncluded: 'getVendorsIncluded',
  getSuggestCompany: 'getSuggestCompany',
  popularPages: 'popularPages',
  homeFeedListByHashtag: 'homeFeedListByHashtag',
  getInvitationList: 'getInvitationList',
  getContactFollowList: 'getContactFollowList',
  notifications: 'notifications:list',
  unSeenNotifications: 'notifications:unSeen',
  unSeenNotificationCounts: 'notifications:unSeenCount',
  getRoomsList: 'getRoomsList',
  getChatRoomsList: 'getChatRoomsList',
  getAllDevices: 'getAllDevices',
  getUserEmails: 'getUserEmails',
  getUser: 'getUser',
  getBlockedObjects: 'getBlockedObjects',
  getFeedOfTaggedMentioned: 'getFeedOfTaggedMentioned',
  getFeedOfHiddenPosts: 'getFeedOfHiddenPosts',
  getSetting: 'getSetting',
  getNotificationSetting: 'getNotificationSetting',
  hashtag: 'hashtag',
  postMenuItemsStatus: 'post:menuItemsStatus',
  getRoomMessages: 'getRoomMessages',
  searchRoomMessages: 'searchRoomMessages',
  staredMessages: 'staredMessages',
  archivedMessages: 'archivedMessages',
  getMyOwnPages: 'getMyOwnPages',
  getRoomDetail: 'getRoomDetail',
  recommendationDetail: 'recommendationDetail',
  getRoomMemberInfo: 'getRoomMemberInfo',
  openJobs: 'openJobs',
  jobDetails: 'jobDetails',
  checkHasJobs: 'checkHasJobs',
  appliedJobs: 'appliedJobs',
  savedJobs: 'savedJobs',
  openSavedJobs: 'openSavedJobs',
  activeAppliedJobs: 'activeAppliedJobs',
  getPagePublishedJobs: 'getPagePublishedJobs',
  appliedSavedJobs: 'appliedSavedJobs',
  closedSavedJobs: 'closedSavedJobs',
  closedAppliedJobs: 'closedAppliedJobs',
  topSuggestionJobs: 'topSuggestionJobs',
  jobPopularCategories: 'jobPopularCategories',
  subCategoryJobList: 'subCategoryJobList',
  searchJobs: 'searchJobs',
  getResume: 'getResume',
  popularJobs: 'PopularJobs',
  recentSearchJobs: 'recentSearchJobs',
  getJobOwnersLis: 'getJobOwnersLis',
  jobCreatorDetail: 'jobCreatorDetail',
  userSavedJobsAvailable: 'userSavedJobsAvailable',
  getAllJobs: 'getAllJobs',
  pageMutual: 'pageMutual',
  peopleMutual: 'peopleMutual',
  getSearchFilters: 'getSearchFilters',
  getWorkingAtCompany: 'getWorkingAtCompany',
  getCurrentTimeZone: 'getCurrentTimeZone',
  schedulesEvents: 'schedulesEvents',
  getUpcomingMeetings: 'getUpcomingMeetings',
  getPastMeetings: 'getPastMeetings',
  getUpcomingAndPastEvents: 'getUpcomingAndPastEvents',
  getAllSearchJobsSuggestions: 'getAllSearchJobsSuggestions',
  unsavedJobsDuringThisSession: 'unsavedJobsDuringThisSession',
  twoFactorAuthData: 'twoFactorAuthData',
  getBackupCode: 'getBackupCode',
  currentEntityId: 'currentEntityId',
  searchTabOrders: 'searchTabOrders',
  schedulesPreferences: 'schedulesPreferences',
  getBookedMeetings: 'getBookedMeetings',
  getPopularPeople: 'getPopularPeople',
  getPopularPeopleCategories: 'getPopularPeopleCategories',
  redirectUrl: 'redirectUrl',
  getAllProviders: {
    [ProviderType.Calendar]: 'getAllProviders.calendar',
    [ProviderType.Conference]: 'getAllProviders.conference',
  },
  getAllProvidersKeys: {
    [ProviderType.Calendar]: 'getAllProvidersKeys.calendar',
    [ProviderType.Conference]: 'getAllProvidersKeys.conference',
  },
  getSimilarCandidates: 'getSimilarCandidates',
  getCandidatesList: 'getCandidatesList',
  getCandidate: 'getCandidate',
  getCandidateSummary: 'getCandidateSummary',
  getCandidateSearchFilters: 'getCandidateSearchFilters',
  getCandidateRecruiterJobsList: 'getCandidateRecruiterJobsList',
  getCandidateAppliedJobsList: 'getCandidateAppliedJobsList',
  getCandidateActivities: 'getCandidateActivities',
  searchCandidateActivities: 'searchCandidateActivities',
  textEditorFocusButton: 'textEditorFocusButton',
  getDeviceId: 'getDeviceId',
  getChatStatus: 'getChatStatus',
  pageEmployees: 'pageEmployees',
  getReferralCode: 'getReferralCode',
  getGoogleConnections: 'getGoogleConnections',
  getConnections: 'getConnections',
  getHashtagsByText: 'getHashtagsByText',
  getFollowersNotFollowedByMe: 'getFollowersNotFollowedByMe',
  invitationFailures: 'invitationFailures',
  getPageDeleteStatus: 'getPageDeleteStatus',
  getHashtagsCount: 'getHashtagsCount',
  getAssociatedPeopleWithPage: 'getAssociatedPeopleWithPage',
  getCookiePolicy: 'getCookiePolicy',
  getFullAccessibilityGuide: 'getFullAccessibilityGuide',
  getPageAccessibilities: 'getPageAccessibilities',
  getProjectsList: 'getProjectsList',
  getJobsList: 'getJobsList',
  jobApplications: 'jobApplications',
  jobCandidates: 'jobCandidates',
  jobCollaborators: 'jobCollaborators',
  jobCompany: 'jobCompany',
  jobActivities: 'jobActivities',
  candidateActivities: 'candidateActivities',
  candidateTodos: 'candidateTodos',
  candidateNotes: 'candidateNotes',
  candidateMeetings: 'candidateMeetings',
  candidateReviewes: 'candidateReviewes',
  candidateMeetinges: 'candidateMeetinges',
  jobReviews: 'jobReviews',
  projectDetails: 'projectDetails',
  projectJobsList: 'projectJobsList',
  projectTodos: 'projectTodos',
  projectMeetings: 'projectMeetings',
  downloadFiles: 'downloadFiles',
  projectActivities: 'projectActivities',
  projectApplications: 'projectApplications',
  projectCandidates: 'projectCandidates',
  companyInfo: 'companyInfo',
  myPages: 'myPages',
  inCompleteProfile: 'inCompleteProfile',
  suggestCandidates: 'suggestCandidates',
  getPipelinesList: 'getPipelinesList',
  getPipeline: 'getPipeline',
  getPipelineParticipants: 'getPipelineParticipants',
  getPrivateFiles: 'getPrivateFiles',
  getPublicFiles: 'getPublicFiles',
  templateFiles: 'templateFiles',
  participationMettingsActivities: 'participationMettingsActivities',
  roomMembers: 'room-members',
  getPipelineActivities: 'getPipelineActivities',
  getCompareUsers: 'getCompareUsers',
  getTicketsList: 'getTicketsList',
  getTicketDetails: 'getTicketDetails',
  getPlansList: 'getPlansList',
  getBillingList: 'getBillingList',
  getBillingDetails: 'getBillingDetails',
  enrollingPlanData: 'enrollingPlanData',
  getEnrolledPlans: 'getEnrolledPlans',
  jobTrackingEmails: 'jobTrackingEmails',
  getInvoiceData: 'getInvoiceData',
  searchAllTemplates: 'searchAllTemplates',
  checkFeature: 'checkFeature',
  getCandidateLastView: 'getCandidateLastView',
  getAllSavedFilters: 'getAllSavedFilters',
  getAllTemplates: 'getAllTemplates',
};

export default QueryKeys;
