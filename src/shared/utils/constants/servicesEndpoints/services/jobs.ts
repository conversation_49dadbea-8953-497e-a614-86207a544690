const JOB_SERVICE = 'job-service';
const JO<PERSON>_SEARCH_SERVICE = 'job-search-service';

const API_VERSION = '/api/v1';

const base = `${JOB_SERVICE}${API_VERSION}`;
const searchBase = `${JOB_SEARCH_SERVICE}${API_VERSION}`;

const jobs = {
  createJob: `${base}/job`,
  getJobsList: `${base}/job`,
  getJobTemplatesGeneral: `${base}/jobs-template-business/description/general`,
  getJobTemplatesPersonal: `${base}//jobs-template-business/description/personal`,
  getJobTemplates: `${base}/jobs-template-business/description/search`,
  getBusinessJobDetails: (id: string): string => `${base}/job/${id}`,
  updateStatus: (id: string): string => `${base}/job/status/${id}`,
  availabilitycheck: `${base}/job/availability-check?status=OPEN`,
  fileUpload: `${base}/resume/upload`,
  getJobsApplicationList: `${base}/application`,
  getJobsSavedList: `${base}/saved`,
  postApplication: `${base}/job/apply`,
  getUserApplications: (userId: string): string =>
    `${base}/application/user/${userId}`,
  withdraw: (id: string): string => `${base}/job/withdraw/${id}`,
  saveJob: `${base}/saved`,
  unSaveJob: (id: string): string => `${base}/saved/${id}`,
  userSavedJobsAvailable: `${base}/saved/availability-check`,
  getJobsTopSuggestionList: `${searchBase}/job/top-suggestion`,
  getJobsPopularList: `${searchBase}/job/popular`,
  getAllJobs: `${searchBase}/job/all`,
  getJobsRecentSearchList: `${searchBase}/recent-search`,
  getUserJobDetails: (id: string): string => `${base}/job/view/${id}`,
  getJobOwnersLis: (id: string): string => `${base}/owner/${id}`,
  postJobOwner: (id: string): string => `${base}/owner/${id}`,
  getPopularCategories: `${searchBase}/category/popular`,
  subCategoryJobList: `${searchBase}/job/category`,
  getResume: (userId: string): string =>
    `${base}/file/latest-resume/${userId}?fileType=RESUME`,
  getJobCreatorDetail: (id: string): string => `${base}/owner/associate/${id}`,
  deleteResume: (id: string): string => `${base}/resume/${id}`,
  shareResume: `${base}/resume/share/`,
  recentSearch: (id: string): string => `${searchBase}/recent-search/${id}`,
  searchUserJobs: `${searchBase}/job/search/user`,
  searchBusinessJobs: `${searchBase}/job/search/business`,
  getSearchUserFilters: `${searchBase}/filter`,
  getSearchBusinessFilters: `${searchBase}/filter`,
  getCompanyFollowerPage: `${base}/jobs-page-user/follower/page`,
  getCompanyFollowerPerson: `${base}/jobs-page-user/follower/person`,
  getCompanyOnLobox: `${base}/jobs-page-user/on-lobox`,
  getWorksHere: `${base}/jobs-page-user/works-here`,
  reportJob: `${base}/jobs-report-user/report`,
  getSearchJobsSuggestions: `${searchBase}/job/suggestions`,
  searchJobsSuggest: `${searchBase}/job/suggest`,
  getJobsByIds: `${searchBase}/job`,
  getPagePublishedJobs: `${base}/published`,
  getJobsForLinking: `${searchBase}/job/search/business`,
  candidacy: `${base}/candidacy`,
  getCandidacyLinkedJobs: (candidateId: string): string =>
    `${base}/candidacy/candidate/${candidateId}`,
  getSingleCandidacyById: (candidateId: string): string =>
    `${base}/candidacy/${candidateId}`,
  getJobProjects: (projectId: string) => `${base}/job/project/${projectId}`,
  updatePriority: (id: string): string => `${base}/job/priority/${id}`,
  resumeAccess: `${base}/preferences/resume-access`,
  searchActivities: `${searchBase}/activity/search`,
  getPipeline: (pipelineId: string): string => `${base}/pipeline/${pipelineId}`,
  moveUserPipeline: (userId: string): string => `${base}/pipeline/${userId}`,
  getPipelineParticipants: (pipelineId: string): string =>
    `${base}/participation/pipeline/${pipelineId}`,
  getJobsAllCandidateIds: `${base}/candidate/id`,
  batchAddCandidatesToJobs: `${base}/candidacies/batch`,
  batchUpdateStatus: `${base}/job/batch/status`,
  batchUpdatePriority: `${base}/job/batch/priority`,
  updateProjectInfo: (id: string): string => `${base}/job/project/${id}`,
  updateBasicInfo: (id: string): string => `${base}/job/${id}`,
  updateGeneralInfo: (id: string): string => `${base}/job/description/${id}`,
  updateManagement: (id: string): string => `${base}/job/additional/${id}`,
  updateApplicationForm: (id: string): string =>
    `${base}/job/application/${id}`,
  updateRequirements: (id: string): string => `${base}/job/requirement/${id}`,
  updateBenefits: (id: string): string => `${base}/job/benefit/${id}`,
  updatePipelines: (id: string): string => `${base}/pipeline/${id}`,
  getTrackingJob: (participationId: string): string =>
    `${base}/tracking/${participationId}`,
  getTrackingJobMeetingsList: (participationId: string) =>
    `${base}/activity/participation/meeting/${participationId}`,
  changePipelineApplicantTrack: (id: string): string =>
    `${base}/pipeline/applicant-track/${id}`,
  changePipelineColor: (id: string): string => `${base}/pipeline/color/${id}`,
  pinCandidate: (id: string): string => `${base}/participation/pin/${id}`,
  searchPipelineParticipations: `${searchBase}/participation/search`,
  searchCandidateParticipations: `${searchBase}/participation/candidate/search`,
  countUserParticipations: (id: string) =>
    `${base}/participation/count/user/${id}`,
  batchMoveCandidatesToStage: `${base}/pipeline/batch`,
  batchAddNoteToCandidates: `${base}/note/batch`,
  batchAddTodoToCandidates: `${base}/todo/batch`,
  addTodo: (participationId: string) => `${base}/todo/${participationId}`,
  getJobTrackingEmails: (participationId: string) =>
    `${base}/message/${participationId}`,
  autoMovement: (pipelineId: string): string =>
    `${base}/pipeline/auto-movement/${pipelineId}`,
  getJobBillingList: `${base}/billing`,
  pipelineBatchReject: `${base}/pipeline/batch/reject`,
};

export default jobs;
