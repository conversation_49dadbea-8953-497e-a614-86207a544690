import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';
import type { SingleJobAPIProps } from '@shared/types/jobsProps';
import type { IconName } from '@shared/uikit/Icon';
import { getEntityShareUrl } from '@shared/utils/getJobShareUrl';
import { ShareEntities, ShareEntityTab } from '@shared/types/share/entities';
import { useQueryClient } from '@tanstack/react-query';
import {
  getAllCandidates,
  getBusinessJobDetails,
} from '@shared/utils/api/jobs';
import {
  getVendorsExcluded,
  getVendorsIncluded,
} from '@shared/utils/api/company';
import { QueryKeys } from '@shared/utils/constants';
import type { AxiosResponse } from 'axios';
import { useRouter } from 'next/navigation';
import useOpenShareWindow from './useOpenShareWindow';
import { openMultiStepForm } from './useMultiStepForm';

type ActionType =
  | 'stages'
  | 'edit'
  | 'share'
  | 'delete'
  | 'shar_via_twit'
  | 'shar_via_face'
  | 'shar_via_linkdin'
  | 'link_candidate'
  | 'submit_to_vendor';

interface IRecruiterJobMoreActions {
  exclude: string[];
}

const useRecruiterJobMoreActions = ({ exclude }: IRecruiterJobMoreActions) => {
  const appDispatch = useGlobalDispatch();
  const openShareWindow = useOpenShareWindow();
  const queryClient = useQueryClient();
  const router = useRouter();

  const onStages = (jobId: string) => {
    router.push(`/pipelines/${jobId}`);
  };
  const onEdit = async (jobId: string) => {
    let jobData = queryClient.getQueryData([
      QueryKeys.jobDetails,
      String(jobId),
    ]) as AxiosResponse<SingleJobAPIProps>;
    if (!jobData?.data) {
      jobData = await queryClient.fetchQuery({
        queryKey: [QueryKeys.jobDetails, String(jobId)],
        queryFn: () => getBusinessJobDetails(jobId),
      });
    }

    openMultiStepForm({
      formName: 'createJobForm',
      data: jobData.data ?? jobData,
      options: {
        step: 1,
      },
    });
  };
  const onShare = (job: SingleJobAPIProps) =>
    appDispatch({
      type: 'SET_SHARE_ENTITY_TABBED_MODAL_DATA',
      payload: {
        isOpen: true,
        tabs: [
          ShareEntityTab.COPY_LINK,
          ShareEntityTab.SHARE_VIA_MESSAGE,
          ShareEntityTab.SHARE_VIA_EMAIL,
        ],
        entityData: {
          attachment: {
            type: ShareEntities.JOB,
            data: {
              ...job,
              category: { label: job.categoryName },
              title: { label: job.title },
              location: { label: job.location?.title },
              workPlaceType: { label: job.workPlaceType },
              isBusiness: true,
            },
          },
        },
      },
    });
  const onDelete = (job: SingleJobAPIProps) =>
    openMultiStepForm({
      formName: 'deleteEntityModal',
      data: job,
      variant: 'deleteJob',
    });
  const onShareViaX = () =>
    openShareWindow('https://twitter.com/share', {
      url: getEntityShareUrl('job'),
    });
  const onShareViaFacebook = () => {
    const params = { u: getEntityShareUrl('job') };

    openShareWindow('https://www.facebook.com/sharer/sharer.php', params);
  };
  const onShareViaLinkedIn = () => {
    const params = { url: getEntityShareUrl('job') };
    openShareWindow('https://linkedin.com/shareArticle', params);
  };

  const onSubmitToVendors = async (jobId: string) => {
    openMultiStepForm({
      formName: 'submitToVendor',
      data: {
        step: '1',
        jobId,
      },
    });
  };

  const onLinkCandidates = async (
    job: SingleJobAPIProps,
    options?: {
      handleRefetch?: () => void;
    }
  ) => {
    const { ids } = await getAllCandidates(job.id);
    openMultiStepForm({
      formName: 'linkJobForm',
      data: {
        id: job.id,
        target: 'job',
        initialJobs: ids,
      },
      options,
    });
  };

  const onAction = async (
    label: ActionType,
    job: SingleJobAPIProps,
    options?: any
  ) => {
    switch (label) {
      case 'edit':
        return onEdit(job.id);
      case 'share':
        return onShare(job);
      case 'delete':
        return onDelete(job);
      case 'shar_via_twit':
        return onShareViaX();
      case 'shar_via_face':
        return onShareViaFacebook();
      case 'shar_via_linkdin':
        return onShareViaLinkedIn();
      case 'link_candidate':
        return onLinkCandidates(job, options);
      case 'submit_to_vendor':
        return onSubmitToVendors(job.id);
      default:
        return onStages(job.id);
    }
  };

  const filteredActions = actions?.filter(
    (item) => !exclude?.includes(item?.label)
  );

  return { actions: filteredActions, onAction };
};

export default useRecruiterJobMoreActions;

const actions: {
  icon: IconName;
  label: ActionType;
  hasDivider?: boolean;
}[] = [
  {
    icon: 'pipeline-light',
    label: 'stages',
  },
  {
    icon: 'link-rotate',
    label: 'link_candidate',
  },
  {
    icon: 'link-rotate',
    label: 'submit_to_vendor',
  },
  {
    icon: 'share',
    label: 'share',
  },
  {
    icon: 'pen-light',
    label: 'edit',
  },
  {
    icon: 'trash',
    label: 'delete',
    hasDivider: true,
  },
  {
    icon: 'twitter',
    label: 'shar_via_twit',
  },
  {
    icon: 'facebook-s',
    label: 'shar_via_face',
  },
  {
    icon: 'linkedin',
    label: 'shar_via_linkdin',
  },
];
