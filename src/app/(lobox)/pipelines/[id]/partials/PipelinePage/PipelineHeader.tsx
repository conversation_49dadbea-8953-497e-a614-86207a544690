import ObjectInfoCard from '@shared/components/molecules/ObjectInfoCard';
import type { JobAPIProps } from '@shared/types/jobsProps';
import IconButton from '@shared/uikit/Button/IconButton';
import Flex from '@shared/uikit/Flex';
import useTranslation from '@shared/utils/hooks/useTranslation';
import Button from '@shared/uikit/Button';
import { openMultiStepForm } from '@shared/hooks/useMultiStepForm';
import BusinessJobCardBadges from '@shared/components/molecules/BusinessJobCard/BaseBusinessJobCard/BusinessJobCardBadges';
import { QueryKeys } from '@shared/utils/constants';
import { useQueryClient } from '@tanstack/react-query';
import type { JobPiplineData } from '@shared/types/pipelineProps';
import useCustomParams from '@shared/utils/hooks/useCustomParams';
import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';

interface PipelineHeaderProps {
  jobData: JobAPIProps;
}

const PipelineHeader = (props: PipelineHeaderProps) => {
  const { jobData } = props;
  const { t } = useTranslation();
  const queryClient = useQueryClient();
  const { handleChangeParams } = useCustomParams();
  const { onAction } = useRecruiterJobMoreActions({
    exclude: ['submit_to_vendor'],
  });
  const onLinkCandidate = () => {
    onAction('link_candidate', jobData as any, {
      handleRefetch: () => {
        queryClient.invalidateQueries({
          queryKey: [QueryKeys.getPipeline, String(jobData.id)],
        });
        handleChangeParams({
          add: { pipelineRefetch: 'REVIEW' },
        });
      },
    });
  };
  const handleUpdateCache = (condition: string, value: string) => {
    const jobDetails = queryClient.getQueryData([
      QueryKeys.getPipeline,
      String(jobData.id),
    ]) as JobPiplineData;
    queryClient.setQueryData([QueryKeys.getPipeline, String(jobData.id)], {
      ...jobDetails,
      job: {
        ...jobDetails.job,
        [condition]: value,
      },
    });
  };
  const openEditModal = () => {
    openMultiStepForm({
      formName: 'createJobForm',
      data: jobData,
      options: {
        step: 1,
      },
    });
  };
  const onOpenStagesModal = () => {
    openMultiStepForm({
      formName: 'createJobForm',
      data: jobData,
      options: {
        subForm: 'pipelines_and_automation',
        step: 4,
      },
    });
  };

  return (
    <Flex className="!flex-1 !flex-row">
      <ObjectInfoCard
        classNames={{ firstLineWrapper: '!gap-8' }}
        firstText={jobData?.title}
        secondText={jobData?.pageTitle}
        withAvatar
        avatar={jobData?.pageCroppedImageUrl}
        isPage
        avatarProps={{ size: 'smd' }}
        visibleFourthText={false}
        isFirstTextSmall
        actions={
          <IconButton
            name="edit"
            type="far"
            size="sm18"
            colorSchema="transparent2"
            noHover
            onClick={openEditModal}
            className="!cursor-pointer"
          />
        }
      />
      <Flex className="!flex-row ml-auto items-center gap-8">
        <BusinessJobCardBadges
          status={jobData.status}
          priority={jobData.priority}
          showBrief
          jobId={jobData.id}
          allProps={{ ...jobData, image: jobData.pageCroppedImageUrl } as any}
          handleUpdateCache={handleUpdateCache}
        />
        <Button
          leftIcon="edit"
          label={t('edit_stages')}
          schema="semi-transparent"
          onClick={onOpenStagesModal}
        />
        <Button
          leftIcon="link"
          label={t('link_candidate')}
          onClick={onLinkCandidate}
        />
      </Flex>
    </Flex>
  );
};

export default PipelineHeader;
