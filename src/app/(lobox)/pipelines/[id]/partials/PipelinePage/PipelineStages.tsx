/* eslint-disable no-restricted-syntax */
import Flex from '@shared/uikit/Flex';
import type { OnDragEndResponder } from '@hello-pangea/dnd';
import { DragDropContext, Droppable } from '@hello-pangea/dnd';
import { use, useEffect, useRef, useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { updateUserPipeline } from '@shared/utils/api/jobs';
import type { JobPiplineData, PipelineInfo } from '@shared/types/pipelineProps';
import { QueryKeys } from '@shared/utils/constants';
import type {
  NormalizedJobParticipationModel,
  PipelineStageFilter,
} from '@shared/types/jobsProps';
import { useSearchParams } from 'next/navigation';
import PipelineStagesHeader from './PipelineStages/PipelineStagesHeader';
import PipelineStage from './PipelineStages/PipelineStage';
import { usePipelinePageCtx } from '../PipelinePageProvider';
import {
  useAutoScrollToCard,
  useScrollToSelectedCandidate,
} from './PipelineStages/utils';

interface IPipelineStagesProps {
  data: JobPiplineData;
}

const PipelineStages: React.FunctionComponent<IPipelineStagesProps> = (
  props
) => {
  const { data } = props;
  const [bulkedStageId, setBulkedStageId] = useState<string>();
  const { refetch, filter } = usePipelinePageCtx();
  const queryClient = useQueryClient();
  const wrapperRef = useRef<HTMLDivElement>(null);
  const horizontalScrollRef = useRef<HTMLDivElement>(null);
  const sourceIdRef = useRef('');
  const destinationIdRef = useRef('');
  const searchParams = useSearchParams();
  const currentEntityId = searchParams.get('currentEntityId');

  const [innerFilter, setInnerFilter] = useState(
    handleCreateFilter(data.pipelines)
  );

  const { mutate } = useMutation({
    mutationFn: updateUserPipeline,
    onSuccess: () => {
      refetch();
    },
  });

  /* --------- Auto-scroll to stage containing the selected candidate --------- */
  useScrollToSelectedCandidate({
    currentEntityId,
    pipelines: data.pipelines,
    horizontalScrollRef,
    filter,
    innerFilter,
  });

  useEffect(
    () => () => {
      queryClient.invalidateQueries({
        queryKey: [QueryKeys.getPipelineParticipants],
        exact: false,
      });
    },
    [queryClient]
  );

  const onDragEnd: OnDragEndResponder<string> = (result) => {
    const { destination, source, draggableId } = result;
    if (!destination || destination.droppableId === source.droppableId) return;
    sourceIdRef.current = source.droppableId;
    destinationIdRef.current = destination.droppableId;

    const sourceStage = queryClient.getQueryData([
      QueryKeys.getPipelineParticipants,
      String(source.droppableId),
      filter,
      innerFilter[source.droppableId as keyof typeof innerFilter],
    ]) as { pages: { content: NormalizedJobParticipationModel[] }[] };
    const destinationStage = queryClient.getQueryData([
      QueryKeys.getPipelineParticipants,
      String(destination.droppableId),
      filter,
      innerFilter[destination.droppableId as keyof typeof innerFilter],
    ]) as { pages: { content: NormalizedJobParticipationModel[] }[] };
    if (!sourceStage?.pages) return;
    let sourcePageIndex = -1;
    let user;
    for (let i = 0; i < sourceStage.pages.length; i += 1) {
      const foundUser = sourceStage.pages[i].content.find(
        (participant) => String(participant.id) === draggableId
      );
      if (foundUser) {
        user = foundUser;
        sourcePageIndex = i;
        break;
      }
    }
    if (!user) return;
    const destinationPageIndex = Math.floor(destination.index / 20);
    const destinationIndexInPage = destination.index % 20;
    queryClient.setQueryData(
      [
        QueryKeys.getPipelineParticipants,
        String(source.droppableId),
        filter,
        innerFilter[source.droppableId as keyof typeof innerFilter],
      ],
      {
        ...sourceStage,
        pages: sourceStage.pages.map((page, i) => ({
          ...page,
          content:
            i === sourcePageIndex
              ? page.content.filter((p) => p.id !== user.id)
              : page.content,
        })),
      }
    );
    if (destinationStage?.pages) {
      while (destinationStage.pages.length <= destinationPageIndex) {
        destinationStage.pages.push({ content: [] });
      }
      queryClient.setQueryData(
        [
          QueryKeys.getPipelineParticipants,
          String(destination.droppableId),
          filter,
          innerFilter[destination.droppableId as keyof typeof innerFilter],
        ],
        {
          ...destinationStage,
          pages: destinationStage.pages.map((page, i) => ({
            ...page,
            content:
              i === destinationPageIndex
                ? [
                    ...page.content.slice(0, destinationIndexInPage),
                    user,
                    ...page.content.slice(destinationIndexInPage),
                  ]
                : page.content,
          })),
        }
      );
    }
    mutate({
      userId: draggableId,
      pipelineId: destination.droppableId,
    });
  };

  useAutoScrollToCard('cards-container2');

  return (
    <Flex
      className="flex-1 gap-12 h-full overflow-y-hidden"
      id="cards-container2"
    >
      <PipelineStagesHeader data={data.job} />
      <Flex className="flex-1 h-full overflow-y-hidden" ref={wrapperRef as any}>
        <Flex
          className="!flex-row gap-12 flex-1 h-full overflow-y-hidden overflow-x-auto"
          ref={horizontalScrollRef} // Add ref for horizontal scrolling
        >
          <DragDropContext onDragEnd={onDragEnd}>
            {data.pipelines.map((stage) => (
              <Droppable
                droppableId={String(stage.id)}
                key={`droppable_${stage.id}`}
              >
                {(provided) => (
                  <PipelineStage
                    stage={stage}
                    draggableProvided={provided}
                    isBulked={bulkedStageId === stage.id}
                    onBulk={setBulkedStageId}
                    wrapperRef={wrapperRef}
                    innerFilter={innerFilter[stage.id]}
                    setInnerFilter={(newFilter) => {
                      setInnerFilter({
                        ...innerFilter,
                        [String(stage.id)]: newFilter,
                      });
                    }}
                    data-stage-id={stage.id}
                    className="min-w-0 flex-shrink-0"
                  />
                )}
              </Droppable>
            ))}
          </DragDropContext>
        </Flex>
      </Flex>
    </Flex>
  );
};

export default PipelineStages;

const handleCreateFilter = (pipelines: PipelineInfo[]) =>
  pipelines.reduce(
    (acc, pipeline) => ({
      ...acc,
      [String(pipeline.id)]: {
        sortBy: 'DATE',
      } as PipelineStageFilter,
    }),
    {}
  );
