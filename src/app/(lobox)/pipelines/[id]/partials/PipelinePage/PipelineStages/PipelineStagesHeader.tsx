import useRecruiterJobMoreActions from '@shared/hooks/useRecruiterJobMoreActions';
import type { JobAPIProps } from '@shared/types/jobsProps';
import IconButton from '@shared/uikit/Button/IconButton';
import Divider from '@shared/uikit/Divider';
import DividerVertical from '@shared/uikit/Divider/DividerVertical';
import Flex from '@shared/uikit/Flex';
import PopperItem from '@shared/uikit/PopperItem';
import PopperMenu from '@shared/uikit/PopperMenu';
import SearchInputV2 from '@shared/uikit/SearchInputV2';
import Switch from '@shared/uikit/Switch';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { use, useCallback, useState } from 'react';
import InfoCard from 'shared/components/Organism/Objects/Common/InfoCard';
import debounce from 'lodash/debounce';
import { useGlobalDispatch } from '@shared/contexts/Global/global.provider';

import { usePipelinePageCtx } from '../../PipelinePageProvider';

interface PipelineStagesHeaderProps {
  data: JobAPIProps;
}

const PipelineStagesHeader: React.FC<PipelineStagesHeaderProps> = ({
  data,
}) => {
  const { t } = useTranslation();
  const { onAction } = useRecruiterJobMoreActions({
    exclude: ['submit_to_vendor'],
  });
  const { filter, setFilter } = usePipelinePageCtx();
  const [searchInput, setSearchInput] = useState(filter.text);
  const debounceFn = useCallback(
    debounce(
      (text: string) =>
        setFilter({ ...filter, text: text.length > 2 ? text : '' }),
      500
    ),
    [filter, setFilter]
  );
  const globalDispatch = useGlobalDispatch();
  const handleSearch = (value: string) => {
    setSearchInput(value);
    debounceFn(value);
  };

  return (
    <InfoCard
      disabledHover
      wrapperClassName="!px-20 !py-12 !items-center !rounded-xl"
    >
      <Flex className="!flex-row gap-8 ">
        <Flex className="bg-brand_10 px-12 h-32 rounded-full">
          <Switch
            label={`${data.applicantsCount} ${t('applicants')}`}
            className="!p-0 gap-12"
            value={filter.containsApplicants}
            onChange={() =>
              setFilter({
                ...filter,
                containsApplicants: !filter.containsApplicants,
              })
            }
          />
        </Flex>
        <Flex className="bg-brand_10 px-12 h-32 rounded-full">
          <Switch
            label={`${data.candidatesCount} ${t('candidates')}`}
            className="!p-0 gap-12"
            value={filter.containsCandidate}
            onChange={() =>
              setFilter({
                ...filter,
                containsCandidate: !filter.containsCandidate,
              })
            }
          />
        </Flex>
        <Flex className="bg-brand_10 px-12 h-32 rounded-full">
          <Switch
            label={`${data.rejectionsCount} ${t('rejections')}`}
            className="!p-0 gap-12"
            value={filter.containsRejected}
            onChange={() =>
              setFilter({
                ...filter,
                containsRejected: !filter.containsRejected,
              })
            }
          />
        </Flex>
      </Flex>
      <SearchInputV2
        placeholder={t('search_candidates')}
        onChange={(value) => handleSearch(value)}
        className="ml-auto min-w-[250px]"
        value={searchInput}
      />
      <DividerVertical className="!h-full" />
      <PopperMenu
        placement="bottom-end"
        closeOnScroll
        buttonComponent={<IconButton type="fas" name="ellipsis-h" size="md" />}
      >
        <PopperItem
          onClick={() =>
            globalDispatch({
              type: 'TOGGLE_COMPARE_MODAL',
              payload: { open: true, data },
            })
          }
          iconName="compare"
          iconType="far"
          label={t('compare')}
          iconSize={20}
        />
        <PopperItem
          onClick={() => onAction('share', data as any)}
          iconName="share"
          iconType="far"
          label={t('share_job')}
          iconSize={20}
        />
        <Divider />
        <PopperItem
          onClick={() => {
            alert('Coming soon');
          }}
          iconName="bottom-panel"
          iconType="far"
          label={t('automation_panel')}
          iconSize={20}
          action={<Switch className="!p-0 flex !flex-grow-[unset] ml-auto" />}
          className="min-w-[280px]"
        />
      </PopperMenu>
    </InfoCard>
  );
};

export default PipelineStagesHeader;
