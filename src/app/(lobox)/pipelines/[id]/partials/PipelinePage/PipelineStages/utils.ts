/* eslint-disable no-restricted-syntax */
import type {
  NormalizedJobParticipationModel,
  PipelineStageFilter,
} from '@shared/types/jobsProps';
import type { PipelineInfo } from '@shared/types/pipelineProps';
import { QueryKeys } from '@shared/utils/constants';
import { useQueryClient } from '@tanstack/react-query';
import { useSearchParams } from 'next/navigation';
import type { RefObject } from 'react';
import { useEffect } from 'react';

interface UseScrollToSelectedCandidateParams {
  currentEntityId: string | null;
  pipelines: PipelineInfo[] | undefined;
  horizontalScrollRef: RefObject<HTMLDivElement>;
  filter: any;
  innerFilter: Record<string, PipelineStageFilter>;
}

export const useScrollToSelectedCandidate = ({
  currentEntityId,
  pipelines,
  horizontalScrollRef,
  filter,
  innerFilter,
}: UseScrollToSelectedCandidateParams) => {
  const queryClient = useQueryClient();

  // eslint-disable-next-line consistent-return
  useEffect(() => {
    if (currentEntityId && horizontalScrollRef.current && pipelines) {
      console.log('Horizontal scroll effect triggered:', {
        currentEntityId,
        pipelines: pipelines?.length,
      });

      /* ------------ Find which stage contains the selected candidate ------------ */
      const findStageWithCandidate = () => {
        let foundStageId = null;

        for (const stage of pipelines) {
          const stageData = queryClient.getQueryData([
            QueryKeys.getPipelineParticipants,
            String(stage.id),
            filter,
            innerFilter[stage.id],
          ]) as { pages: { content: NormalizedJobParticipationModel[] }[] };

          if (stageData?.pages) {
            const foundInStage = stageData?.pages?.some((page) =>
              page.content.some((candidate) => {
                const candidateId =
                  candidate?.candidate?.id || candidate?.applicant?.id;
                const match = candidateId === currentEntityId;
                if (match) {
                  console.log(
                    `Found candidate ${currentEntityId} in stage ${stage.id}`
                  );
                }
                return match;
              })
            );

            if (foundInStage) {
              foundStageId = stage?.id;
              break;
            }
          }
        }

        if (foundStageId) {
          scrollToStage(foundStageId);
        } else {
          const selectedCard = document.querySelector(
            `[data-candidate-id="${currentEntityId}"]`
          );
          if (selectedCard) {
            const stageContainer = selectedCard.closest('[data-stage-id]');
            if (stageContainer) {
              const stageId = stageContainer.getAttribute('data-stage-id');
              if (stageId) {
                scrollToStage(stageId);
              }
            }
          }
        }
      };

      const scrollToStage = (stageId: string) => {
        const stageElement = document.querySelector(
          `[data-stage-id="${stageId}"]`
        );
        console.log('Stage element found:', !!stageElement);

        if (stageElement && horizontalScrollRef.current) {
          const container = horizontalScrollRef.current;
          const containerRect = container.getBoundingClientRect();
          const stageRect = stageElement.getBoundingClientRect();

          /* -------------- Calculate scroll position to center the stage ------------- */
          const stageOffsetLeft = (stageElement as HTMLElement).offsetLeft;
          const scrollLeft =
            stageOffsetLeft - containerRect.width / 2 + stageRect.width / 2;

          container.scrollTo({
            left: Math.max(0, scrollLeft),
            behavior: 'smooth',
          });
        }
      };

      findStageWithCandidate();

      const timeoutId1 = setTimeout(findStageWithCandidate, 300);
      const timeoutId2 = setTimeout(findStageWithCandidate, 600);

      return () => {
        clearTimeout(timeoutId1);
        clearTimeout(timeoutId2);
      };
    }
  }, [
    currentEntityId,
    pipelines,
    queryClient,
    filter,
    innerFilter,
    horizontalScrollRef,
  ]);
};

export const useAutoScrollToCard = (
  containerId: string = 'cards-container'
) => {
  const searchParams = useSearchParams();
  const currentEntityId = searchParams.get('currentEntityId');

  useEffect(() => {
    if (currentEntityId) {
      setTimeout(() => {
        const cardElement = document.getElementById(`card-${currentEntityId}`);
        const containerElement = document.getElementById(containerId);

        if (cardElement && containerElement) {
          const cardRect = cardElement.getBoundingClientRect();
          const containerRect = containerElement.getBoundingClientRect();

          const centerX =
            cardElement.offsetLeft -
            containerRect.width / 2 +
            cardRect.width / 2;
          const centerY =
            cardElement.offsetTop -
            containerRect.height / 2 +
            cardRect.height / 2;

          containerElement.scrollTo({
            left: Math.max(0, centerX),
            top: Math.max(0, centerY),
            behavior: 'smooth',
          });
        }
      }, 150);
    }
  }, [currentEntityId, containerId]);

  return currentEntityId;
};
