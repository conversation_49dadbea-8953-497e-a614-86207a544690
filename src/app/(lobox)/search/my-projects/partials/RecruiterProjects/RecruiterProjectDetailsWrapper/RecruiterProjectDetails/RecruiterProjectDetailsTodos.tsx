import TodoItem from '@shared/components/molecules/TodoItem';
import EmptySearchResult from '@shared/components/Organism/EmptySearchResult';
import { QueryKeys } from '@shared/utils/constants';
import useTranslation from '@shared/utils/hooks/useTranslation';
import { useState, type FC } from 'react';
import SearchList from '@shared/components/Organism/SearchList';
import Flex from '@shared/uikit/Flex';
import Switch from '@shared/uikit/Switch';
import usePaginateQuery from '@shared/utils/hooks/usePaginateQuery';
import translateReplacer from '@shared/utils/toolkit/translateReplacer';
import Button from '@shared/uikit/Button';
import { getCandidatesPagedTodos } from '@shared/utils/api/candidates';
import classes from './RecruiterProjectDetailsStyles.module.scss';

interface RecruiterProjectDetailsTodosProps {
  projectId: string;
}

const RecruiterProjectDetailsTodos: FC<RecruiterProjectDetailsTodosProps> = ({
  projectId,
}) => {
  const { t } = useTranslation();
  const [onlyDone, setOnlyDone] = useState(false);
  const { content, totalElements, totalPages, setPage, page, isLoading } =
    usePaginateQuery<any>({
      action: {
        apiFunc: getCandidatesPagedTodos,
        key: [QueryKeys.projectTodos, projectId, onlyDone],
      },
    });

  const handleCreate = () => alert('TODO: needs to be implemented later!');

  return (
    <Flex className={classes.meetingsRoot}>
      <Switch
        label={t('show_completed_Todos')}
        className={classes.switchBox}
        disabled={!onlyDone && !content?.length}
        onChange={setOnlyDone}
        value={onlyDone}
      />
      <SearchList
        entity="todos"
        isLoading={isLoading}
        totalElements={Number(totalElements)}
        data={content}
        onPageChange={setPage}
        totalPages={Number(totalPages)}
        className={{ root: classes.childrenBox }}
        noItemButtonAction
        renderItem={(todo) => (
          <TodoItem
            item={todo}
            key={`todo_${todo.id}`}
            cardWrapperProps={{
              classNames: { root: classes.jobItem },
            }}
          />
        )}
        emptyList={
          <EmptySearchResult
            title={t('no_todos')}
            sectionMessage={translateReplacer(t('no_antity_in_object'), [
              t('todos').toLowerCase(),
              t('create_one').toLowerCase(),
            ])}
            className={classes.emptyList}
            classNames={{ description: '!mt-12' }}
          >
            <Button
              label={t('create')}
              className="mt-20"
              leftIcon="plus"
              leftType="far"
              onClick={handleCreate}
            />
          </EmptySearchResult>
        }
        parentPage={page}
      />
    </Flex>
  );
};

export default RecruiterProjectDetailsTodos;
